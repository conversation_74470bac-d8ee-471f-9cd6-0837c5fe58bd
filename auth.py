#!/usr/bin/env python3
"""
Authentication module for Rethink BH API.
Handles login, session management, and token generation.
"""

import os
import logging
import requests
import re
from typing import Optional, Dict, Any
from google.cloud import secretmanager

# Configure logging
logger = logging.getLogger(__name__)

class RethinkAuthError(Exception):
    """Custom exception for Rethink authentication operations."""
    pass

class RethinkAuth:
    """Handles authentication and session management for Rethink BH API."""
    
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "https://webapp.rethinkbehavioralhealth.com"
        self.headers = {
            "Content-Type": "application/json;charset=utf-8",
            "Accept": "application/json, text/plain, */*",
            "X-Application-Key": "74569e11-18b4-4122-a58d-a4b830aa12c4",
            "X-Origin": "Angular",
            "User-Agent": "Mozilla/5.0 (X11; Linux x86_64; rv:139.0) Gecko/139.0",
            "Origin": self.base_url,
            "Referer": f"{self.base_url}/Healthcare#/Login",
        }
        self._authenticated = False

    def _get_secret(self, secret_name: str, project_id: Optional[str] = None) -> str:
        """Retrieve secret from Google Cloud Secret Manager."""
        try:
            if project_id is None:
                project_id = os.getenv('GOOGLE_CLOUD_PROJECT')
                if not project_id:
                    raise RethinkAuthError("GOOGLE_CLOUD_PROJECT environment variable not set")
            
            client = secretmanager.SecretManagerServiceClient()
            name = f"projects/{project_id}/secrets/{secret_name}/versions/latest"
            response = client.access_secret_version(request={"name": name})
            return response.payload.data.decode("UTF-8")
        except Exception as e:
            logger.error(f"Failed to retrieve secret {secret_name}")
            raise RethinkAuthError(f"Secret retrieval failed: {str(e)}")

    def get_credentials(self) -> tuple[str, str]:
        """Get credentials from environment or Secret Manager."""
        # Try environment variables first (for local development)
        email = os.getenv("RTHINK_USER")
        password = os.getenv("RTHINK_PASS")

        # If all environment variables are found, use them
        if all([email, password]):
            logger.info("Using credentials from environment variables")
            return email, password

        # If not found, try Secret Manager
        try:
            if not email:
                email = self._get_secret("RTHINK_USER")
            if not password:
                password = self._get_secret("RTHINK_PASS")
        except RethinkAuthError as e:
            # If Secret Manager fails and we don't have env vars, raise error
            if not all([email, password]):
                raise RethinkAuthError("Missing required credentials and Secret Manager unavailable")

        if not all([email, password]):
            raise RethinkAuthError("Missing required credentials")

        return email, password

    def _fetch_token(self) -> Optional[str]:
        """Extract anti-forgery token from session cookies."""
        for cookie in self.session.cookies:
            if any(k in cookie.name.upper() for k in ("XSRF", "ANTIFORGERY", "REQUESTVERIFICATIONTOKEN")):
                return cookie.value
        return None

    def _with_token(self, headers: dict) -> dict:
        """Add anti-forgery token to headers."""
        token = self._fetch_token()
        if not token:
            raise RethinkAuthError("No anti-forgery token found in cookies")
        return {**headers, "X-XSRF-TOKEN": token}

    def authenticate(self, email: str = None, password: str = None) -> None:
        """Authenticate with Rethink BH."""
        if not email or not password:
            email, password = self.get_credentials()
            
        logger.info("Starting authentication with Rethink BH")
        
        try:
            # Initial request to get session
            self.session.get(f"{self.base_url}/HealthCare", headers=self.headers).raise_for_status()
            
            # Verify email
            self.session.post(
                f"{self.base_url}/HealthCare/SingleSignOn/GetAuthenticationDetail",
                json={"User": email}, 
                headers=self._with_token(self.headers)
            ).raise_for_status()
            
            # Login
            self.session.post(
                f"{self.base_url}/HealthCare/User/Login",
                json={"User": email, "Password": password, "setPermissions": True},
                headers=self._with_token(self.headers)
            ).raise_for_status()
            
            # Final authentication step
            self.session.get(
                f"{self.base_url}/core/scheduler/appointments",
                headers=self._with_token(self.headers)
            ).raise_for_status()
            
            self._authenticated = True
            logger.info("Authentication successful")
            
        except requests.RequestException as e:
            logger.error("Authentication failed")
            raise RethinkAuthError(f"Authentication failed: {str(e)}")

    def get_tokens(self) -> tuple[str, str]:
        """
        Get both XSRF and MVC tokens for API requests.
        
        Returns:
            Tuple of (xsrf_token, mvc_token)
        """
        if not self._authenticated:
            raise RethinkAuthError("Must authenticate before getting tokens")
            
        # Get XSRF token from existing session
        fresh_headers = self._with_token(self.headers)
        xsrf_token = fresh_headers.get("X-XSRF-TOKEN", "")
        
        # Get fresh MVC token from dashboard page
        logger.info("Getting fresh MVC token from reporting dashboard page")
        dashboard_page_response = self.session.get(
            f"{self.base_url}/Healthcare/ReportingDashboard",
            headers=fresh_headers
        )
        
        mvc_token = None
        if dashboard_page_response.status_code == 200:
            # Extract MVC token from the page content
            page_content = dashboard_page_response.text
            # Look for the token in various possible formats
            mvc_token_patterns = [
                r'name="__RequestVerificationToken"[^>]*value="([^"]+)"',
                r'"__RequestVerificationToken":"([^"]+)"',
                r'__RequestVerificationToken["\s]*:["\s]*([^"]+)',
            ]
            
            for pattern in mvc_token_patterns:
                match = re.search(pattern, page_content)
                if match:
                    mvc_token = match.group(1)
                    logger.info("Found fresh MVC token from dashboard page")
                    break
                    
        # Ensure we have different tokens (this is crucial!)
        if not mvc_token or mvc_token == xsrf_token:
            logger.warning("MVC token same as XSRF or not found, this may cause API errors")
            mvc_token = xsrf_token  # Fallback, but this might not work
            
        return xsrf_token, mvc_token

    def prepare_headers(self, additional_headers: dict = None) -> dict:
        """
        Prepare headers for API requests with proper tokens.
        
        Args:
            additional_headers: Additional headers to include
            
        Returns:
            Dictionary of headers ready for API requests
        """
        if not self._authenticated:
            raise RethinkAuthError("Must authenticate before preparing headers")
            
        xsrf_token, mvc_token = self.get_tokens()
        
        # Base headers for API requests
        api_headers = {
            "Accept": "application/json, text/plain, */*",
            "Accept-Encoding": "gzip, deflate, br, zstd", 
            "Accept-Language": "en-US,en;q=0.5",
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "application/json;charset=utf-8",
            "DNT": "1",
            "Host": "webapp.rethinkbehavioralhealth.com",
            "Origin": self.base_url,
            "Pragma": "no-cache",
            "Priority": "u=0",
            "Referer": f"{self.base_url}/Healthcare",
            "RefererFullUrl": f"{self.base_url}/Healthcare#/ReportingDashboard/13",
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors", 
            "Sec-Fetch-Site": "same-origin",
            "Sec-GPC": "1",
            "User-Agent": self.headers["User-Agent"],
            "X-Application-Key": self.headers["X-Application-Key"],
            "X-Origin": self.headers["X-Origin"],
            "X-XSRF-TOKEN": xsrf_token,
            "X-XSRF-MVC-TOKEN": mvc_token
        }
        
        # Add any additional headers
        if additional_headers:
            api_headers.update(additional_headers)
            
        logger.info(f"Prepared headers with XSRF token: {xsrf_token[:20] if xsrf_token else 'None'}...")
        logger.info(f"Prepared headers with MVC token: {mvc_token[:20] if mvc_token else 'None'}...")
        
        return api_headers

    def visit_pages(self, pages: list[str] = None) -> None:
        """
        Visit specific pages to establish proper session context.
        
        Args:
            pages: List of page paths to visit (optional)
        """
        if not self._authenticated:
            raise RethinkAuthError("Must authenticate before visiting pages")
            
        default_pages = [
            "/Healthcare",
            "/Healthcare/ReportingDashboard",
            "/Healthcare/ReportingDashboard/Report/13"
        ]
        
        pages_to_visit = pages or default_pages
        
        for page in pages_to_visit:
            try:
                logger.info(f"Visiting page: {page}")
                response = self.session.get(
                    f"{self.base_url}{page}",
                    headers=self._with_token(self.headers)
                )
                # Don't fail if specific pages don't work, just log it
                if response.status_code != 200:
                    logger.warning(f"Page {page} returned status {response.status_code}")
                else:
                    logger.debug(f"Successfully visited {page}")
            except Exception as e:
                logger.warning(f"Failed to visit page {page}: {str(e)}")

    @property
    def is_authenticated(self) -> bool:
        """Check if currently authenticated."""
        return self._authenticated

    @property
    def session_obj(self) -> requests.Session:
        """Get the authenticated session object."""
        if not self._authenticated:
            raise RethinkAuthError("Must authenticate before accessing session")
        return self.session
