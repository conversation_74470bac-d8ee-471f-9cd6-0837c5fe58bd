#!/usr/bin/env python3
"""
Over Term Dashboard module for Rethink BH API.
Handles fetching dashboard data for clients with Over Term authorization status.
"""

import logging
from datetime import datetime
from typing import Dict, Any, List
from auth import RethinkAuth

# Configure logging
logger = logging.getLogger(__name__)

class OverTermDashboardError(Exception):
    """Custom exception for Over Term dashboard operations."""
    pass

class OverTermDashboard:
    """Handles Over Term dashboard data fetching from Rethink BH API."""
    
    def __init__(self, auth: RethinkAuth = None):
        """
        Initialize Over Term Dashboard.
        
        Args:
            auth: Optional RethinkAuth instance. If not provided, creates a new one.
        """
        self.auth = auth or RethinkAuth()
        
    def get_dashboard_data(
        self,
        start_date: str = None,
        end_date: str = None,
        client_ids: List[int] = None
    ) -> Dict[str, Any]:
        """Fetch Over Term dashboard data for ABA clients."""
        logger.info("Fetching Over Term dashboard data")

        # Set defaults
        start_date = start_date or "07/01/2024"
        end_date = end_date or "07/31/2025"
        client_ids = client_ids or [325526, 349284, 304907, 297808]

        payload = {
            "reportId": 13,
            "printMode": 0,
            "filters": {
                "AuthorizationStatus": [1],  # Over Term
                "ServiceLines": [6476],      # ABA
                "Locations": [],
                "Funders": [],
                "Clients": client_ids,
                "ClientsInit": [],
                "ClientStatus": [1],         # Active
                "StartDate": start_date,
                "EndDate": end_date,
                "IncludeDemoClients": False,
                "ClientDateFormat": "MM/dd/yyyy"
            },
            "clientDateFormat": "MM/dd/yyyy"
        }

        try:
            response = self.auth.make_request(
                "POST",
                f"{self.auth.base_url}/HealthCare/ReportingDashboard/GetDashboardReport",
                request_type="dashboard",
                json=payload,
                timeout=120
            )

            dashboard_data = response.json()
            auth_details = dashboard_data.get("Reports", {}).get("ReportData", {}).get("AuthorizationUtilizationDetails", [])

            logger.info(f"Retrieved {len(auth_details)} authorization records")

            return {
                "status": "success",
                "data": dashboard_data,
                "filters_applied": payload["filters"],
                "summary": {
                    "total_authorizations": len(auth_details),
                    "clients_found": len(set(detail.get("ClientName", "") for detail in auth_details)),
                    "date_range": f"{start_date} to {end_date}"
                },
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Dashboard request failed: {e}")
            raise OverTermDashboardError(f"Dashboard request failed: {e}")


