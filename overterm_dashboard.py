#!/usr/bin/env python3
"""
Over Term Dashboard module for Rethink BH API.
Handles fetching dashboard data for clients with Over Term authorization status.
"""

import logging
import requests
from datetime import datetime
from typing import Dict, Any, List, Optional
from auth import RethinkAuth, RethinkAuthError

# Configure logging
logger = logging.getLogger(__name__)

class OverTermDashboardError(Exception):
    """Custom exception for Over Term dashboard operations."""
    pass

class OverTermDashboard:
    """Handles Over Term dashboard data fetching from Rethink BH API."""
    
    def __init__(self, auth: RethinkAuth = None):
        """
        Initialize Over Term Dashboard.
        
        Args:
            auth: Optional RethinkAuth instance. If not provided, creates a new one.
        """
        self.auth = auth or RethinkAuth()
        
    def get_dashboard_data(
        self, 
        start_date: str = None, 
        end_date: str = None, 
        client_ids: List[int] = None,
        authorization_status: List[int] = None,
        service_lines: List[int] = None
    ) -> Dict[str, Any]:
        """
        Fetch dashboard data for ABA clients whose authorization is Over Term.
        
        Args:
            start_date: Start date in MM/dd/yyyy format (optional, defaults to broad range)
            end_date: End date in MM/dd/yyyy format (optional, defaults to broad range)
            client_ids: List of specific client IDs to filter for (optional, defaults to known Over Term clients)
            authorization_status: List of authorization status codes (optional, defaults to [1] for Over Term)
            service_lines: List of service line IDs (optional, defaults to [6476] for ABA)
        
        Returns:
            Dictionary containing the dashboard report data
        
        Raises:
            OverTermDashboardError: If the request fails or authentication is required
        """
        logger.info("Fetching Over Term dashboard data from Rethink BH")
        
        # Ensure authentication
        if not self.auth.is_authenticated:
            logger.info("Not authenticated, performing authentication")
            self.auth.authenticate()
        
        # Set default date range if not provided (use broad range to catch Over Term clients)
        if not start_date or not end_date:
            start_date = "07/01/2024"  # Start from last year to catch Over Term authorizations
            end_date = "07/31/2025"    # End next year to be comprehensive
        
        # Set default client IDs if not provided (known Over Term clients)
        if not client_ids:
            client_ids = [325526, 349284, 304907, 297808]  # Default Over Term clients
        
        # Set default authorization status (Over Term)
        if not authorization_status:
            authorization_status = [1]  # 1 = Over Term status
            
        # Set default service lines (ABA)
        if not service_lines:
            service_lines = [6476]  # ABA service line
        
        # Construct payload exactly matching working browser request
        payload = {
            "reportId": 13,
            "printMode": 0,
            "filters": {
                "AuthorizationStatus": authorization_status,
                "ServiceLines": service_lines,
                "Locations": [],
                "Funders": [],
                "Clients": client_ids,
                "ClientsInit": [],
                "ClientStatus": [1],  # Active clients
                "StartDate": start_date,
                "EndDate": end_date,
                "IncludeDemoClients": False,
                "ClientDateFormat": "MM/dd/yyyy"
            },
            "clientDateFormat": "MM/dd/yyyy"
        }
        
        try:
            # Visit pages to establish proper session context
            self.auth.visit_pages([
                "/Healthcare",
                "/Healthcare/ReportingDashboard",
                "/Healthcare/ReportingDashboard/Report/13"
            ])

            # Prepare headers with proper tokens
            headers = self.auth.prepare_headers()
            
            logger.info("Making dashboard request with authenticated session")
            logger.debug(f"Request payload: {payload}")

            response = self.auth.session_obj.post(
                f"{self.auth.base_url}/HealthCare/ReportingDashboard/GetDashboardReport",
                json=payload,
                headers=headers,
                timeout=120,
            )

            if response.status_code != 200:
                logger.error(f"Dashboard request failed with status {response.status_code}")
                logger.error(f"Response headers: {dict(response.headers)}")
                logger.error(f"Response content: {response.text}")
                response.raise_for_status()

            # Parse JSON response
            dashboard_data = response.json()
            
            # Extract useful metrics
            report_data = dashboard_data.get("Reports", {}).get("ReportData", {})
            auth_details = report_data.get("AuthorizationUtilizationDetails", [])
            
            logger.info(f"Successfully fetched dashboard data with {len(auth_details)} authorization records")
            
            return {
                "status": "success",
                "data": dashboard_data,
                "filters_applied": payload["filters"],
                "summary": {
                    "total_authorizations": len(auth_details),
                    "clients_found": len(set(detail.get("ClientName", "") for detail in auth_details)),
                    "date_range": f"{start_date} to {end_date}"
                },
                "timestamp": datetime.now().isoformat()
            }

        except requests.RequestException as e:
            logger.error("Dashboard request failed")
            raise OverTermDashboardError(f"Dashboard request failed: {str(e)}")
        except Exception as e:
            logger.error("Unexpected error during dashboard fetch")
            raise OverTermDashboardError(f"Dashboard fetch failed: {str(e)}")

    def get_client_summary(
        self, 
        start_date: str = None, 
        end_date: str = None, 
        client_ids: List[int] = None
    ) -> Dict[str, Any]:
        """
        Get a summary of Over Term clients with key metrics.
        
        Args:
            start_date: Start date in MM/dd/yyyy format
            end_date: End date in MM/dd/yyyy format  
            client_ids: List of client IDs to analyze
            
        Returns:
            Dictionary with client summary data
        """
        dashboard_result = self.get_dashboard_data(start_date, end_date, client_ids)
        
        if dashboard_result["status"] != "success":
            return dashboard_result
            
        auth_details = dashboard_result["data"].get("Reports", {}).get("ReportData", {}).get("AuthorizationUtilizationDetails", [])
        
        # Group by client
        clients = {}
        for detail in auth_details:
            client_name = detail.get("ClientName", "Unknown")
            if client_name not in clients:
                clients[client_name] = {
                    "client_name": client_name,
                    "funder_name": detail.get("FunderName", ""),
                    "authorizations": [],
                    "total_auth_hours": 0,
                    "total_sched_hours": 0,
                    "total_verified_hours": 0,
                    "days_until_expiration": detail.get("DaysUntilExpiration", 0)
                }
            
            clients[client_name]["authorizations"].append({
                "authorization_number": detail.get("AuthorizationNumber", ""),
                "service_name": detail.get("ServiceName", ""),
                "bill_code": detail.get("BillCode", ""),
                "auth_hours": detail.get("TotalAuthHours", 0),
                "sched_hours": detail.get("SchedHours", 0),
                "verified_hours": detail.get("VerifiedHours", 0),
                "authorization_status": detail.get("AuthorizationStatus", ""),
                "dates": detail.get("Dates", "")
            })
            
            # Accumulate totals
            clients[client_name]["total_auth_hours"] += detail.get("TotalAuthHours", 0)
            clients[client_name]["total_sched_hours"] += detail.get("SchedHours", 0)
            clients[client_name]["total_verified_hours"] += detail.get("VerifiedHours", 0)
        
        return {
            "status": "success",
            "summary": {
                "total_clients": len(clients),
                "total_authorizations": len(auth_details),
                "date_range": dashboard_result["summary"]["date_range"]
            },
            "clients": list(clients.values()),
            "timestamp": datetime.now().isoformat()
        }

    def get_authorization_details(
        self, 
        client_id: int, 
        start_date: str = None, 
        end_date: str = None
    ) -> Dict[str, Any]:
        """
        Get detailed authorization information for a specific client.
        
        Args:
            client_id: Specific client ID to get details for
            start_date: Start date in MM/dd/yyyy format
            end_date: End date in MM/dd/yyyy format
            
        Returns:
            Dictionary with detailed authorization data for the client
        """
        dashboard_result = self.get_dashboard_data(start_date, end_date, [client_id])
        
        if dashboard_result["status"] != "success":
            return dashboard_result
            
        auth_details = dashboard_result["data"].get("Reports", {}).get("ReportData", {}).get("AuthorizationUtilizationDetails", [])
        
        if not auth_details:
            return {
                "status": "success",
                "message": f"No Over Term authorization data found for client ID {client_id}",
                "client_id": client_id,
                "authorizations": [],
                "timestamp": datetime.now().isoformat()
            }
        
        # All details should be for the same client, get client info from first record
        first_detail = auth_details[0]
        
        return {
            "status": "success",
            "client_info": {
                "client_id": client_id,
                "client_name": first_detail.get("ClientName", ""),
                "funder_name": first_detail.get("FunderName", ""),
                "service_line": first_detail.get("ServiceLine", "")
            },
            "authorizations": auth_details,
            "summary": {
                "total_authorizations": len(auth_details),
                "total_auth_hours": sum(detail.get("TotalAuthHours", 0) for detail in auth_details),
                "total_sched_hours": sum(detail.get("SchedHours", 0) for detail in auth_details),
                "total_verified_hours": sum(detail.get("VerifiedHours", 0) for detail in auth_details)
            },
            "timestamp": datetime.now().isoformat()
        }
